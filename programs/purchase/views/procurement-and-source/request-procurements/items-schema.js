import _ from 'lodash';
import RequestProcurementsItemInfoCellRenderer from './_item-info-cell-renderer';

export default function (vm) {
    const self = vm;

    return {
        _id: {type: 'string', required: false, column: {hidden: true}},
        // requestId: {type: 'string', required: false, column: {hidden: true}},
        requestId: {
            type: 'string',
            label: 'Purchase request',
            required: false,
            column: {
                populate: 'request',
                width: 120,
                visible: false
            },
            editor: {
                collection: 'purchase.requests',
                view: 'purchase.purchase.requests',
                labelFrom: 'code',
                disabled: true
            }
        },
        requestedByEmployeeId: {
            type: 'string',
            required: false,
            column: {hidden: true}
        },
        requestedByUserId: {
            type: 'string',
            required: false,
            column: {hidden: true}
        },
        productId: {type: 'string', required: false, column: {hidden: true}},
        vendor: {
            type: 'object',
            required: false,
            blackbox: true,
            column: {hidden: true}
        },
        vendorName: {
            type: 'string',
            required: false,
            column: {
                hidden: true,
                valueGetter(params) {
                    const data = params.data;

                    if (_.isPlainObject(data) && _.isPlainObject(data.vendor)) {
                        return `${data.vendor.code} - ${data.vendor.name}`;
                    }

                    return '';
                }
            }
        },
        branchId: {type: 'string', required: false, column: {hidden: true}},
        warehouseId: {type: 'string', required: false, column: {hidden: true}},
        taxId: {type: 'string', required: false, column: {hidden: true}},
        unitId: {type: 'string', required: false, column: {hidden: true}},
        baseUnitId: {type: 'string', required: false, column: {hidden: true}},
        product: {
            type: 'string',
            label: 'Product',
            required: false,
            column: {
                pinned: 'left',
                lockVisible: true,
                suppressMovable: true,
                checkCell: true,
                headerCheckboxSelection: true,
                headerCheckboxSelectionFilteredOnly: true,
                minWidth: 180,
                relationParams(params) {
                    const data = params.data;
                    if (_.isPlainObject(data) && _.isPlainObject(data.product)) {
                        return {
                            view: 'inventory.catalog.products',
                            template: data.product.displayName,
                            id: data.productId
                        };
                    }

                    return {};
                },
                valueGetter(params) {
                    const data = params.data;

                    if (_.isPlainObject(data) && _.isPlainObject(data.product)) {
                        return data.product.displayName;
                    }

                    return '';
                }
            },
            editor: {
                disabled: true
            }
        },
        vendorId: {
            type: 'string',
            label: 'Vendor',
            required: false,
            column: {
                pinned: 'left',
                lockVisible: true,
                suppressMovable: true,
                checkCell: true,
                headerCheckboxSelection: true,
                headerCheckboxSelectionFilteredOnly: true,
                populate: 'vendor',
                minWidth: 180,
                hidden: vm.payload.type === 'vendor'
            },
            editor: {
                collection: 'kernel.partners',
                view: 'partners.partners',
                filters(row) {
                    if (Array.isArray(row.allowedVendorIds) && row.allowedVendorIds.length > 0) {
                        return {
                            _id: {$in: row.allowedVendorIds},
                            type: 'vendor'
                        };
                    }

                    return {type: 'vendor'};
                },
                extraFields: ['code'],
                template: '{{code}} - {{name}}',
                updateParams(params, type) {
                    params.model = {type: 'vendor'};

                    return params;
                }
            }
        },
        relatedPartnerId: {
            type: 'string',
            label: 'Related partner',
            required: false,
            column: {
                pinned: 'left',
                populate: 'relatedPartner',
                minWidth: 180,
                visible: false
            },
            editor: {
                collection: 'kernel.partners',
                view: 'partners.partners',
                filters(row) {
                    return {type: {$in: ['vendor', 'customer']}};
                },
                extraFields: ['code'],
                template: '{{code}} - {{name}}',
                updateParams(params, type) {
                    params.model = {type: 'customer'};

                    return params;
                }
            }
        },
        unit: {
            type: 'string',
            label: 'Unit',
            required: false,
            column: {
                valueGetter(params) {
                    const data = params.data;

                    if (_.isPlainObject(data.unit)) {
                        return data.unit.name;
                    }

                    return '';
                },
                width: 90
            },
            editor: {
                disabled: true
            }
        },
        quantity: {
            type: 'decimal',
            label: 'Required quantity',
            min: 0,
            required: false,
            column: {
                width: 120,
                format: 'unit',
                formatOptions(row) {
                    return {
                        number: {
                            precision: self.$setting('system.unitPrecision')
                        }
                    };
                }
            },
            editor: {
                disabled: true
            }
        },
        approvedQuantity: {
            type: 'decimal',
            label: 'Approved quantity',
            min: 0,
            required: false,
            column: {
                width: 120,
                format: 'unit',
                formatOptions(row) {
                    return {
                        number: {
                            precision: self.$setting('system.unitPrecision')
                        }
                    };
                }
            }
        },
        openQuantity: {
            type: 'decimal',
            label: 'Open quantity',
            min: 0,
            required: false,
            column: {
                width: 120,
                format: 'unit',
                formatOptions(row) {
                    return {
                        number: {
                            precision: self.$setting('system.unitPrecision')
                        }
                    };
                }
            },
            editor: {
                disabled: true
            }
        },
        completedQuantity: {
            type: 'decimal',
            label: 'Completed quantity',
            min: 0,
            default: 0,
            column: {
                width: 120,
                format: 'unit',
                formatOptions(row) {
                    return {
                        number: {
                            precision: self.$setting('system.unitPrecision')
                        }
                    };
                }
            },
            editor: {
                disabled: true
            }
        },
        requiredDate: {
            type: 'date',
            label: 'Required date',
            default: 'date:now',
            column: {
                width: 120
            },
            editor: {
                disabled: true
            }
        },
        recordDate: {
            type: 'date',
            label: 'Request date',
            default: 'date:now',
            column: {
                width: 120
            },
            editor: {
                disabled: true
            }
        },
        destinationCode: {
            type: 'string',
            label: 'Destination',
            required: false,
            column: {
                width: 150,
                relationParams(params) {
                    const data = params.data;

                    if (_.isPlainObject(data) && !!data.destinationType) {
                        if (data.destinationType === 'transport') {
                            return {
                                view: 'inventory.operations.transports',
                                id: data.destinationId,
                                customLabel: data.destinationCode,
                                template: data.destinationCode
                            };
                        } else if (data.destinationType === 'quotation') {
                            return {
                                view: 'purchase.purchase.quotations',
                                id: data.destinationId,
                                customLabel: data.destinationCode,
                                template: data.destinationCode
                            };
                        } else if (data.destinationType === 'order') {
                            return {
                                view: 'purchase.purchase.orders',
                                id: data.destinationId,
                                customLabel: data.destinationCode,
                                template: data.destinationCode
                            };
                        } else {
                            return {
                                view: 'accounting.purchase.vendor-invoices',
                                id: data.destinationId,
                                customLabel: data.destinationCode,
                                template: data.destinationCode
                            };
                        }
                    }

                    return {};
                }
            },
            editor: {
                disabled: true
            }
        },
        warehouse: {
            type: 'string',
            label: 'Warehouse',
            required: false,
            column: {
                valueGetter(params) {
                    const data = params.data;

                    if (_.isPlainObject(data) && _.isPlainObject(data.warehouse)) {
                        return `${data.warehouse.shortName} - ${data.warehouse.name}`;
                    }

                    return '';
                },
                visible: false,
                width: 180
            },
            editor: {
                disabled: true
            }
        },
        branch: {
            type: 'string',
            label: 'Branch office',
            required: false,
            column: {
                valueGetter(params) {
                    const data = params.data;

                    if (_.isPlainObject(data) && _.isPlainObject(data.branch)) {
                        return data.branch.name;
                    }

                    return '';
                },
                visible: false,
                width: 180
            },
            editor: {
                disabled: true
            }
        },
        unitPrice: {
            type: 'decimal',
            label: 'Unit Price',
            required: false,
            column: {
                format: 'currency',
                visible: false,
                minWidth: 120
            },
            editor: {
                disabled: true
            }
        },
        discount: {
            type: 'decimal',
            label: 'Discount %',
            required: false,
            column: {
                format: 'percentage',
                minWidth: 120,
                formatOptions(row) {
                    return {
                        number: {
                            precision: vm.$setting('system.percentagePrecision')
                        }
                    };
                },
                hidden: !vm.$setting('purchase.lineDiscounts'),
                visible: false
            },
            editor: {
                disabled: true
            }
        },
        unitPriceAfterDiscount: {
            type: 'decimal',
            label: 'Unit Price After Discount',
            required: false,
            column: {
                format: 'currency',
                visible: false,
                minWidth: 120
            },
            editor: {
                disabled: () => true
            }
        },
        grossUnitPriceAfterDiscount: {
            type: 'decimal',
            label: 'Gross Unit Price After Discount',
            required: false,
            column: {
                format: 'currency',
                visible: false,
                minWidth: 120
            },
            editor: {
                disabled: () => true
            }
        },
        tax: {
            type: 'string',
            label: 'Tax',
            required: false,
            column: {
                valueGetter(params) {
                    const data = params.data;

                    if (_.isPlainObject(data) && _.isPlainObject(data.tax)) {
                        return data.tax.name;
                    }

                    return '';
                },
                minWidth: 120,
                visible: false
            },
            editor: {
                disabled: () => true
            }
        },
        taxTotal: {
            type: 'decimal',
            label: 'Tax Total',
            required: false,
            column: {
                format: 'currency',
                visible: false,
                minWidth: 90
            },
            editor: {
                disabled: () => true
            }
        },
        grossTotal: {
            type: 'decimal',
            label: 'Gross Total',
            required: false,
            column: {
                format: 'currency',
                visible: false,
                minWidth: 120
            },
            editor: {
                disabled: () => true
            }
        },
        total: {
            type: 'decimal',
            label: 'Total',
            required: false,
            column: {
                format: 'currency',
                visible: false,
                minWidth: 120
            },
            editor: {
                disabled: () => true
            }
        },
        status: {
            type: 'string',
            label: 'Status',
            required: false,
            column: {
                width: 120,
                tagsCell: true,
                translateLabels: true,
                tagLabels: [
                    {value: 'open', label: 'Open', color: 'default'},
                    {value: 'processing', label: 'Processing', color: 'success'}
                ]
            },
            editor: {
                disabled: true
            }
        },
        cancelingReason: {
            type: 'string',
            label: 'Canceling reason',
            required: false,
            column: {
                minWidth: 210,
                visible: false
            },
            editor: {
                disabled: true
            }
        },
        canceledBy: {
            type: 'string',
            label: 'Canceled by',
            required: false,
            column: {
                minWidth: 180,
                visible: false
            },
            editor: {
                disabled: true
            }
        },
        allowedVendorIds: {
            type: ['string'],
            default: [],
            column: {
                hidden: true
            }
        },
        deliveryNote: {
            type: 'string',
            label: 'Delivery note',
            required: false,
            column: {
                width: 200,
                visible: false
            },
            editor: {}
        },
        'popup-edit-cell': {
            type: 'string',
            required: false,
            column: {
                headerName: '',
                field: 'popup-edit-cell',
                width: 35,
                maxWidth: 35,
                pinned: 'right',
                lockPosition: false,
                lockVisible: true,
                lockPinned: true,
                editable: false,
                cellClass: 'popup-edit-cell',
                headerClass: 'popup-edit-header-cell',
                resizable: false,
                suppressNavigable: true,
                suppressMovable: true,
                suppressSizeToFit: true,
                sortable: false,
                suppressMenu: true,
                suppressColumnsToolPanel: true,
                headerComponentParams: {
                    template:
                        '<div class="ag-cell-label-container" role="presentation">' +
                        '  <span ref="eMenu" class="ag-header-icon ag-header-cell-menu-button"></span>' +
                        '  <div ref="eLabel" class="ag-header-cell-label" role="presentation">' +
                        `     <span class="ag-header-cell-text" role="columnheader"></span>` +
                        '  </div>' +
                        '</div>'
                },
                cellRendererFramework: RequestProcurementsItemInfoCellRenderer,
                // cellRenderer(params) {
                //     const data = params.data;
                //
                //     if (_.isObject(data) && data._id) {
                //         return '<i class="fas fa-info-circle" style="color: #0078d4 !important; cursor: pointer;"></i>';
                //     }
                //
                //     return '';
                // },
                cellStyle() {
                    return {
                        // 'background-color': '#fff !important',
                        // 'color': '#212121 !important'
                    };
                }
            },
            editor: {
                disabled: true
            }
        }
    };
}
