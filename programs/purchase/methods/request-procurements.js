import _ from 'lodash';
import Random from 'framework/random';
import {mergeAdditionalInformation} from '../../system/helpers/additional-information';
import r from 'nodemailer/lib/smtp-connection';

export default [
    {
        name: 'request-procurements-get-pvr',
        async action(payload, params) {
            const app = this.app;
            const user = params.user;
            const query = {
                ...(payload.query || {}),
                status: {$in: ['open', 'processing']}
            };
            const result = {products: [], vendors: [], requests: []};

            // Get branch ids.
            let branchIds = [];
            if (_.isObject(query.branchId) && Array.isArray(query.branchId.$in)) {
                branchIds = query.branchId.$in;

                delete query.branchId;
            } else if (Array.isArray(query.$and)) {
                query.$and.forEach(q => {
                    if (_.isObject(q.branchId) && Array.isArray(q.branchId.$in)) {
                        branchIds = q.branchId.$in;
                    }
                });

                query.$and = query.$and.filter(q => !_.isObject(q.branchId));
            }
            if (branchIds.length < 1) {
                branchIds = user.branchIds;
            }
            query.branchId = {$in: branchIds};

            // Fix query.
            if (Array.isArray(query.$and) && query.$and.length === 0) {
                delete query.$and;
            }

            if (_.isPlainObject(user) && !!user.partnerId && app.setting('purchase.purchaseOrganizations')) {
                const organization = await app.collection('kernel.organizations').findOne({
                    'team.partnerId': user.partnerId,
                    scope: 'purchase'
                });

                if (_.isPlainObject(organization)) {
                    const team = organization.team || [];
                    const currentMember = team.find(m => m.partnerId === user.partnerId);
                    let organizationSettings = organization.settings || {};

                    if (
                        _.isObject(currentMember) &&
                        _.isObject(currentMember.settings) &&
                        !_.isEmpty(currentMember.settings)
                    ) {
                        organizationSettings = _.assign(organizationSettings, currentMember.settings);
                    }

                    if (_.isObject(organizationSettings) && !_.isEmpty(organizationSettings)) {
                        query.productGroupIds = {
                            $in: organizationSettings.productGroupIds || []
                        };
                    }
                }
            }

            const productIds = (
                await app.collection('purchase.request-procurements').distinct('productId', query)
            ).filter(id => !!id);
            if (productIds.length > 0) {
                const products = await app.collection('inventory.products').find({
                    _id: {$in: productIds},
                    $select: ['_id', 'code', 'name', 'displayName']
                });
                for (const product of products) {
                    result.products.push(product);
                }
            }

            const vendorIds = (
                await app.collection('purchase.request-procurements').distinct('vendorId', query)
            ).filter(id => !!id);
            if (vendorIds.length > 0) {
                const vendors = await app.collection('kernel.partners').find({
                    _id: {$in: vendorIds},
                    $select: ['_id', 'code', 'name']
                });
                for (const vendor of vendors) {
                    result.vendors.push(vendor);
                }
            }

            const requestIds = (
                await app.collection('purchase.request-procurements').distinct('requestId', query)
            ).filter(id => !!id);
            if (requestIds.length > 0) {
                const requests = await app.collection('purchase.requests').find({
                    _id: {$in: requestIds},
                    $select: ['_id', 'code']
                });
                for (const request of requests) {
                    result.requests.push(request);
                }
            }

            return result;
        }
    },
    {
        name: 'request-procurements-get-items',
        async action(payload, params) {
            const app = this.app;
            const user = params.user;
            const query = {
                status: {$in: ['open', 'processing']},
                ...(payload.query || {})
            };

            // Get branch ids.
            let branchIds = [];
            if (_.isObject(query.branchId) && Array.isArray(query.branchId.$in)) {
                branchIds = query.branchId.$in;

                delete query.branchId;
            } else if (Array.isArray(query.$and)) {
                query.$and.forEach(q => {
                    if (_.isObject(q.branchId) && Array.isArray(q.branchId.$in)) {
                        branchIds = q.branchId.$in;
                    }
                });

                query.$and = query.$and.filter(q => !_.isObject(q.branchId));
            }
            if (branchIds.length < 1) {
                branchIds = user.branchIds;
            }
            query.branchId = {$in: branchIds};

            // Fix query.
            if (Array.isArray(query.$and) && query.$and.length === 0) {
                delete query.$and;
            }

            if (_.isPlainObject(user) && !!user.partnerId && app.setting('purchase.purchaseOrganizations')) {
                const organization = await app.collection('kernel.organizations').findOne({
                    'team.partnerId': user.partnerId,
                    scope: 'purchase'
                });

                if (_.isPlainObject(organization)) {
                    const team = organization.team || [];
                    const currentMember = team.find(m => m.partnerId === user.partnerId);
                    let organizationSettings = organization.settings || {};

                    if (
                        _.isObject(currentMember) &&
                        _.isObject(currentMember.settings) &&
                        !_.isEmpty(currentMember.settings)
                    ) {
                        organizationSettings = _.assign(organizationSettings, currentMember.settings);
                    }

                    if (_.isObject(organizationSettings) && !_.isEmpty(organizationSettings)) {
                        query.productGroupIds = {
                            $in: organizationSettings.productGroupIds || []
                        };
                    }
                }
            }

            return await app.collection('purchase.request-procurements').find(query);
        }
    },
    {
        name: 'request-procurements-update-item',
        async action(payload, params) {
            const app = this.app;

            await app.collection('purchase.request-procurements').patch(
                {_id: payload._id},
                {
                    vendorId: payload.vendorId,
                    approvedQuantity: payload.approvedQuantity,
                    openQuantity: payload.openQuantity
                }
            );
        }
    },
    {
        name: 'request-procurements-convert',
        async action(payload, params) {
            const app = this.app;
            const collection = app.collection('purchase.request-procurements');
            const {conversionType, ids} = payload;
            const user = params.user;
            const company = await app.collection('kernel.company').findOne({});
            const requestProcurements = await collection.find({
                _id: {$in: ids}
            });
            const requestProcurementsOperations = [];
            const requestOperations = [];
            const requestIdsToUpdate = [];

            // Get numbering.
            let numbering = null;
            if (conversionType === 'to-quotation') {
                numbering = await app.collection('kernel.numbering').findOne(
                    {
                        code: 'purchaseQuotationNumbering',
                        $select: ['_id']
                    },
                    {
                        disableInUseCheck: true,
                        disableActiveCheck: true,
                        disableSoftDelete: true
                    }
                );
            } else if (conversionType === 'to-order') {
                numbering = await app.collection('kernel.numbering').findOne(
                    {
                        code: 'purchaseOrderNumbering',
                        $select: ['_id']
                    },
                    {
                        disableInUseCheck: true,
                        disableActiveCheck: true,
                        disableSoftDelete: true
                    }
                );
            } else {
                numbering = await app.collection('kernel.numbering').findOne(
                    {
                        code: 'accountingVendorInvoiceNumbering',
                        $select: ['_id']
                    },
                    {
                        disableInUseCheck: true,
                        disableActiveCheck: true,
                        disableSoftDelete: true
                    }
                );
            }

            // Get organization.
            let organizationId = null;
            let purchaseManagerId = null;
            let purchaseRepresentativeId = null;
            let organizationSettings = null;
            if (_.isPlainObject(user) && !!user.partnerId && app.setting('purchase.purchaseOrganizations')) {
                const organization = await app.collection('kernel.organizations').findOne({
                    'team.partnerId': user.partnerId,
                    scope: 'purchase'
                });

                if (_.isPlainObject(organization)) {
                    const team = organization.team || [];
                    const currentMember = team.find(m => m.partnerId === user.partnerId);

                    organizationId = organization._id;
                    organizationSettings = organization.settings || {};

                    if (_.isPlainObject(currentMember)) {
                        if (currentMember.managerId) {
                            purchaseManagerId = currentMember.managerId;
                        } else {
                            purchaseManagerId = currentMember.partnerId;
                        }

                        purchaseRepresentativeId = currentMember.partnerId;
                    }

                    if (
                        _.isPlainObject(currentMember) &&
                        _.isObject(currentMember.settings) &&
                        !_.isEmpty(currentMember.settings)
                    ) {
                        organizationSettings = _.assign(organizationSettings, currentMember.settings);
                    }
                }
            }

            // Group documents according to the vendor and branch.
            const groupedDocuments = Object.values(
                _.groupBy(requestProcurements, pr => {
                    return pr.branchId + '|' + pr.vendorId;
                })
            );

            const requestRelatedDocumentsToAdd = [];

            for (const documentItems of groupedDocuments) {
                const d = {};

                // Get vendor.
                const vendor = await app.collection('kernel.partners').findOne({
                    _id: documentItems[0].vendorId,
                    $select: [
                        '_id',
                        'code',
                        'name',
                        'groupId',
                        'accountingAccountId',
                        'invoiceScenario',
                        'eInvoiceTypeId',
                        'paymentTermId',
                        'address',
                        'tags'
                    ],
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });

                // Get warehouse.
                const warehouses = await app.collection('inventory.warehouses').find({
                    _id: {
                        $in: _.uniq(documentItems.map(di => di.warehouseId))
                    },
                    $select: ['_id', 'address'],
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });
                const warehousesMap = {};
                for (const wh of warehouses) {
                    warehousesMap[wh._id] = wh;
                }
                const warehouse = warehousesMap[documentItems[0].warehouseId];

                // Get products.
                const products = await app.collection('inventory.products').find({
                    _id: {
                        $in: _.uniq(documentItems.map(item => item.productId))
                    },
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });
                const productsMap = {};
                for (const product of products) {
                    productsMap[product._id] = product;
                }

                // Get journal.
                const journal = await app.collection('accounting.journals').findOne({
                    type: 'purchase',
                    branchId: documentItems[0].branchId,
                    $or: [
                        {currencyId: documentItems[0].currencyId},
                        {currencyId: {$exists: false}},
                        {currencyId: ''},
                        {currencyId: null}
                    ],
                    $select: ['_id']
                });

                // Organization default settings.
                if (!_.isEmpty(organizationSettings)) {
                    const settings = organizationSettings;

                    if (settings.defaultDocumentTypeId) d.documentTypeId = settings.defaultDocumentTypeId;
                    if (settings.defaultVendorGroupId) d.partnerGroupId = settings.defaultVendorGroupId;

                    if (settings.defaultPaymentTermId) {
                        d.paymentTermId = settings.defaultPaymentTermId;
                        d.paymentTerm = await app.collection('finance.payment-terms').findOne({
                            _id: d.paymentTermId
                        });
                    }

                    if (settings.defaultListPriceId) d.listPriceId = settings.defaultListPriceId;
                }

                const isForeignPartner =
                    !!vendor.address &&
                    !!vendor.address.countryId &&
                    vendor.address.countryId !== company.address.countryId;
                const documentType = await app.collection('purchase.document-types').findOne({
                    code: isForeignPartner ? 'foreignPurchases' : 'domesticPurchases',
                    $select: ['_id']
                });

                // General
                d.status = 'draft';
                d.code = await app.rpc('kernel.common.request-number', {
                    numberingId: numbering._id,
                    date: app.datetime.local().toJSDate(),
                    save: true
                });
                d.partnerId = vendor._id;
                if (!d.documentTypeId) d.documentTypeId = documentType._id;
                d.partnerGroupId = vendor.groupId ? vendor.groupId : d.partnerGroupId;
                d.currencyId = documentItems[0].currencyId;
                d.currencyRate = documentItems[0].currencyRate;
                d.branchId = documentItems[0].branchId;
                d.recordDate = documentItems[0].recordDate;
                d.expiryDate = documentItems[0].expiryDate;
                d.scheduledDate = app.datetime.fromJSDate(documentItems[0].requiredDate).startOf('day').toJSDate();
                d.organizationId = organizationId;
                d.purchaseManagerId = purchaseManagerId;
                d.purchaseRepresentativeId = purchaseRepresentativeId;
                d.financialProjectId = documentItems[0].financialProjectId;
                if (conversionType === 'to-quotation') {
                    d.quotationDate = documentItems[0].issueDate;
                } else if (conversionType === 'to-order') {
                    d.orderDate = documentItems[0].issueDate;
                } else {
                    d.issueDate = documentItems[0].issueDate;
                }
                d.dueDate = d.issueDate;
                d.relatedDocuments = [];
                d.paymentPlan = null;
                d.paymentPlanningDate = null;

                // Logistic
                if (_.isObject(warehouse)) {
                    d.warehouseId = warehouse._id;
                    d.deliveryAddress = warehouse.address;
                } else {
                    throw new app.errors.Unprocessable(
                        app.translate('No warehouse that is belongs to the selected branch is found.')
                    );
                }

                // Finance
                if (vendor.paymentTermId) {
                    d.paymentTermId = vendor.paymentTermId;
                    d.paymentTerm = await app.collection('finance.payment-terms').findOne({
                        _id: d.paymentTermId
                    });
                } else if (!d.paymentTermId) {
                    d.paymentTerm = await app.collection('finance.payment-terms').findOne({
                        system: true
                    });
                    d.paymentTermId = d.paymentTerm._id;
                }
                const listPrice = await app.collection('purchase.list-prices').findOne({
                    partnerId: d.partnerId,
                    $select: ['_id']
                });
                if (_.isPlainObject(listPrice)) {
                    if (_.isObject(organizationSettings) && !_.isEmpty(organizationSettings)) {
                        // noinspection JSObjectNullOrUndefined
                        if ((organizationSettings.listPriceIds || []).indexOf(listPrice._id) !== -1) {
                            d.listPriceId = listPrice._id;
                        }
                    } else {
                        d.listPriceId = listPrice._id;
                    }
                }

                // Accounting.
                if (_.isObject(journal)) {
                    d.journalId = journal._id;
                    d.journalDescription = `${app.translate('Vendor Invoices')} / ${vendor.code} - ${vendor.name}`;
                }
                d.accountingAccountId = vendor.accountingAccountId;
                d.invoiceScenario = vendor.invoiceScenario;
                d.eInvoiceTypeId = vendor.eInvoiceTypeId;
                if (vendor.address) {
                    d.invoiceAddress = vendor.address;
                }

                // Exchange rates.
                await (async () => {
                    const currencies = await app.collection('kernel.currencies').find({
                        $select: ['name']
                    });
                    const exchangeRates = [];
                    const payloads = [];

                    for (const currency of currencies) {
                        if (currency.name === company.currency.name) {
                            continue;
                        }

                        payloads.push({
                            from: currency.name,
                            to: company.currency.name,
                            value: 1,
                            options: {
                                date: documentItems[0].recordDate
                            }
                        });
                    }

                    for (const payload of await app.rpc('kernel.common.convert-currencies', payloads)) {
                        exchangeRates.push({
                            currencyName: payload.from,
                            rate: payload.rate
                        });
                    }

                    d.exchangeRates = exchangeRates;
                })();

                // Items.
                const items = [];
                for (const documentItem of documentItems) {
                    d.relatedDocuments.push({
                        collection: 'purchase.requests',
                        view: 'purchase.purchase.requests',
                        title: 'Requests',
                        ids: [documentItem.requestId]
                    });

                    const itemWarehouse = warehousesMap[documentItem.warehouseId];
                    const itemProduct = productsMap[documentItem.productId];
                    let item = {};

                    const purchaseNote = (itemProduct.purchaseNote || '').trim();
                    const description = !!purchaseNote ? purchaseNote : itemProduct.displayName;

                    item.id = Random.id(16);
                    item.productId = documentItem.productId;
                    item.productCode = itemProduct.code;
                    item.productDefinition = itemProduct.definition;
                    item.productCategoryId = itemProduct.categoryId;
                    item.productCategoryPath = itemProduct.categoryPath;
                    item.productType = documentItem.productType;
                    item.description = description;
                    item.barcode = documentItem.barcode;
                    item.supplierCatalogNo = documentItem.supplierCatalogNo;
                    item.scheduledDate = app.datetime.fromJSDate(documentItem.requiredDate).startOf('day').toJSDate();
                    item.branchId = documentItem.branchId;
                    item.warehouseId = documentItem.warehouseId;
                    item.quantity = documentItem.approvedQuantity;
                    item.unitId = documentItem.unitId;
                    item.baseUnitId = documentItem.baseUnitId;
                    item.baseQuantity = app.round(
                        (documentItem.baseQuantity * documentItem.approvedQuantity) / documentItem.quantity,
                        'unit'
                    );
                    item.currencyId = d.currencyId;
                    item.currencyRate = d.currencyRate;
                    item.unitPrice = documentItem.unitPrice;
                    item.unitPriceFC = documentItem.unitPrice;
                    item.discount = documentItem.discount;
                    item.unitPriceAfterDiscount = 0;
                    item.grossUnitPriceAfterDiscount = 0;
                    item.taxId = documentItem.taxId;
                    item.taxDetail = null;
                    item.taxTotal = 0;
                    item.grossTotal = 0;
                    item.realTotal = 0;
                    item.stockQuantity = 0;
                    item.orderedQuantity = 0;
                    item.assignedQuantity = 0;
                    item.availableQuantity = 0;
                    item.warehouseStockQuantity = 0;
                    item.warehouseOrderedQuantity = 0;
                    item.warehouseAssignedQuantity = 0;
                    item.warehouseAvailableQuantity = 0;
                    item.financialProjectId = documentItem.financialProjectId;
                    item.total = 0;
                    item.totalFC = 0;
                    item.partnerId = d.partnerId;
                    item.invoiceAddress = d.invoiceAddress;
                    item.deliveryAddress = itemWarehouse.address;
                    item.deliveryNote = documentItem.deliveryNote || '';

                    const um = (itemProduct.unitMeasurements || []).find(m => m.unitId === item.unitId);
                    const quantity = item.quantity || 1;
                    if (!!um) {
                        const units = await app.collection('kernel.units').find();
                        const unitsMap = {};
                        for (const unit of units) {
                            unitsMap[unit._id] = unit;
                        }
                        const lengthUnit = units.find(u => u.category === 'length' && u.symbol === 'm');
                        const weightUnit = units.find(u => u.category === 'weight' && u.symbol === 'kg');

                        if (!!lengthUnit && !!weightUnit) {
                            item.height = 0;
                            item.heightUnitId = lengthUnit._id;
                            item.width = 0;
                            item.widthUnitId = lengthUnit._id;
                            item.depth = 0;
                            item.depthUnitId = lengthUnit._id;
                            item.netWeight = 0;
                            item.netWeightUnitId = weightUnit._id;
                            item.grossWeight = 0;
                            item.grossWeightUnitId = weightUnit._id;

                            if (um.height && um.heightUnitId) {
                                item.height = um.height * quantity;
                                item.heightUnitId = um.heightUnitId;
                            }
                            if (um.width && um.widthUnitId) {
                                item.width = um.width * quantity;
                                item.widthUnitId = um.widthUnitId;
                            }
                            if (um.depth && um.depthUnitId) {
                                item.depth = um.depth * quantity;
                                item.depthUnitId = um.depthUnitId;
                            }
                            if (um.netWeight && um.netWeightUnitId) {
                                item.netWeight = um.netWeight * quantity;
                                item.netWeightUnitId = um.netWeightUnitId;
                            }
                            if (um.grossWeight && um.grossWeightUnitId) {
                                item.grossWeight = um.grossWeight * quantity;
                                item.grossWeightUnitId = um.grossWeightUnitId;
                            }

                            const heightUnit = unitsMap[item.heightUnitId];
                            if (!!heightUnit) {
                                if (heightUnit.type === 'smaller') item.height = item.height / heightUnit.ratio;
                                else if (heightUnit.type === 'bigger') item.height = item.height * heightUnit.ratio;
                            }
                            const widthUnit = unitsMap[item.widthUnitId];
                            if (!!widthUnit) {
                                if (widthUnit.type === 'smaller') item.width = item.width / widthUnit.ratio;
                                else if (widthUnit.type === 'bigger') item.width = item.width * widthUnit.ratio;
                            }
                            const depthUnit = unitsMap[item.depthUnitId];
                            if (!!depthUnit) {
                                if (depthUnit.type === 'smaller') item.depth = item.depth / depthUnit.ratio;
                                else if (depthUnit.type === 'bigger') item.depth = item.depth * depthUnit.ratio;
                            }
                            const netWeightUnit = unitsMap[item.netWeightUnitId];
                            if (!!netWeightUnit) {
                                if (netWeightUnit.type === 'smaller')
                                    item.netWeight = item.netWeight / netWeightUnit.ratio;
                                else if (netWeightUnit.type === 'bigger')
                                    item.netWeight = item.netWeight * netWeightUnit.ratio;
                            }
                            const grossWeightUnit = unitsMap[item.grossWeightUnitId];
                            if (!!grossWeightUnit) {
                                if (grossWeightUnit.type === 'smaller')
                                    item.grossWeight = item.grossWeight / grossWeightUnit.ratio;
                                else if (grossWeightUnit.type === 'bigger')
                                    item.grossWeight = item.grossWeight * grossWeightUnit.ratio;
                            }

                            item.heightUnitId = lengthUnit._id;
                            item.widthUnitId = lengthUnit._id;
                            item.depthUnitId = lengthUnit._id;
                            item.netWeightUnitId = weightUnit._id;
                            item.grossWeightUnitId = weightUnit._id;
                        }
                    }

                    if (
                        Array.isArray(itemProduct.purchaseAdditionalTaxIds) &&
                        itemProduct.purchaseAdditionalTaxIds.length > 0
                    ) {
                        const taxIds = [item.taxId, ...itemProduct.purchaseAdditionalTaxIds];
                        const taxes = await app.collection('kernel.taxes').find({
                            _id: {$in: taxIds},
                            $select: ['_id', 'name'],
                            $disableActiveCheck: true,
                            $disableSoftDelete: true
                        });

                        item.taxPayload = {
                            taxIds,
                            description: taxes.map(tax => tax.name).join(', ')
                        };
                    }

                    item.brandId = itemProduct.brandId;
                    item.shippingUnitId = itemProduct.shippingUnitId;
                    item.typeOfGoodsId = itemProduct.typeOfGoodsId;
                    item.manufacturerId = itemProduct.manufacturerId;
                    item.manufacturerProductCode = itemProduct.manufacturerProductCode;
                    item.countryOfManufactureId = itemProduct.countryOfManufactureId;
                    item.containerTypeId = itemProduct.containerTypeId;
                    item.containerNo = itemProduct.containerNo;
                    item.containerBrand = itemProduct.containerBrand;
                    item.containerQuantity = 1;
                    item.hsCode = itemProduct.hsCode;
                    item.classificationCode = itemProduct.classificationCode;
                    item.classificationVersion = itemProduct.classificationVersion;
                    item.classificationValue = itemProduct.classificationValue;
                    item.countryOfOriginId = itemProduct.countryOfOriginId;

                    requestProcurementsOperations.push({
                        updateOne: {
                            filter: {_id: documentItem._id},
                            update: {
                                $set: {
                                    destinationItemId: item.id
                                }
                            }
                        }
                    });

                    // Stocks.
                    if (item.productId) {
                        const report = await app.rpc('inventory.get-stock-report', {
                            date: item.scheduledDate || app.datetime.local().toJSDate(),
                            productId: item.productId
                        });

                        if (Array.isArray(report) && report.length > 0) {
                            const r = report[0];

                            item.stockQuantity = r.stockQuantity;
                            item.orderedQuantity = r.orderedQuantity;
                            item.assignedQuantity = r.assignedQuantity;
                            item.availableQuantity = r.availableQuantity;
                        } else {
                            item.stockQuantity = 0;
                            item.orderedQuantity = 0;
                            item.assignedQuantity = 0;
                            item.availableQuantity = 0;
                        }
                    }
                    if (item.warehouseId && item.productId) {
                        const report = await app.rpc('inventory.get-stock-report', {
                            date: item.scheduledDate || app.datetime.local().toJSDate(),
                            productId: item.productId,
                            warehouseId: item.warehouseId
                        });

                        if (Array.isArray(report) && report.length > 0) {
                            const r = report[0];

                            item.warehouseStockQuantity = r.stockQuantity;
                            item.warehouseOrderedQuantity = r.orderedQuantity;
                            item.warehouseAssignedQuantity = r.assignedQuantity;
                            item.warehouseAvailableQuantity = r.availableQuantity;
                        } else {
                            item.warehouseStockQuantity = 0;
                            item.warehouseOrderedQuantity = 0;
                            item.warehouseAssignedQuantity = 0;
                            item.warehouseAvailableQuantity = 0;
                        }
                    }

                    // Calculate unit price.
                    if (conversionType === 'to-quotation') {
                        item.unitPrice = await app.rpc(
                            'purchase.calculate-quotation-row-unit-price',
                            {
                                row: item,
                                field: 'productId',
                                model: d
                            },
                            {user}
                        );
                    } else if (conversionType === 'to-order') {
                        item.unitPrice = await app.rpc(
                            'purchase.calculate-order-row-unit-price',
                            {
                                row: item,
                                field: 'productId',
                                model: d
                            },
                            {user}
                        );
                    } else {
                        item.unitPrice = await app.rpc(
                            'accounting.calculate-vendor-invoice-row-unit-price',
                            {
                                row: item,
                                field: 'productId',
                                model: d
                            },
                            {user}
                        );
                    }

                    // Calculate discount.
                    if (conversionType === 'to-quotation') {
                        item = await app.rpc(
                            'purchase.calculate-quotation-row-discount',
                            {
                                row: item,
                                field: 'productId',
                                model: d
                            },
                            {user: params.user}
                        );
                    } else if (conversionType === 'to-order') {
                        item = await app.rpc(
                            'purchase.calculate-order-row-discount',
                            {
                                row: item,
                                field: 'productId',
                                model: d
                            },
                            {user: params.user}
                        );
                    } else {
                        item = await app.rpc(
                            'accounting.calculate-vendor-invoice-row-discount',
                            {
                                row: item,
                                field: 'productId',
                                model: d
                            },
                            {user: params.user}
                        );
                    }

                    // Apply contract.
                    item = await app.rpc(
                        'purchase.apply-contract-to-row',
                        {
                            row: item,
                            field: 'productId',
                            model: d
                        },
                        {user: params.user}
                    );

                    // Calculate row totals.
                    if (conversionType === 'to-quotation') {
                        item = await app.rpc(
                            'purchase.calculate-quotation-row-totals',
                            {
                                row: item,
                                field: 'productId',
                                model: d
                            },
                            {user}
                        );
                    } else if (conversionType === 'to-order') {
                        item = await app.rpc(
                            'purchase.calculate-order-row-totals',
                            {
                                row: item,
                                field: 'productId',
                                model: d
                            },
                            {user}
                        );
                    } else {
                        item = await app.rpc(
                            'accounting.calculate-vendor-invoice-row-totals',
                            {
                                row: item,
                                field: 'productId',
                                model: d
                            },
                            {user}
                        );
                    }

                    items.push(item);
                }
                d.items = items;

                // Calculate discounts.
                let discountListQuery = {
                    $or: [
                        {type: 'all-vendors'},
                        {type: 'vendor', partnerId: vendor._id},
                        {
                            type: 'vendor-groups',
                            partnerGroupIds: vendor.groupId
                        },
                        {
                            type: 'vendor-tags',
                            partnerTags: {
                                $in: (vendor.tags || []).map(tag => tag.label)
                            }
                        }
                    ]
                };
                if (conversionType === 'to-quotation') {
                    discountListQuery = {
                        ...discountListQuery,
                        startDate: {
                            $lte: d.quotationDate
                        },
                        endDate: {
                            $gte: d.quotationDate
                        }
                    };
                } else if (conversionType === 'to-order') {
                    discountListQuery = {
                        ...discountListQuery,
                        startDate: {
                            $lte: d.orderDate
                        },
                        endDate: {
                            $gte: d.orderDate
                        }
                    };
                } else {
                    discountListQuery = {
                        ...discountListQuery,
                        startDate: {
                            $lte: d.issueDate
                        },
                        endDate: {
                            $gte: d.issueDate
                        }
                    };
                }
                const discountList = await app.collection('purchase.general-discount-lists').findOne(discountListQuery);
                let discount = 0;
                if (_.isPlainObject(discountList) && _.isNumber(discountList.discount)) {
                    discount = discountList.discount;
                }

                // Calculate totals.
                let subTotal = 0;
                let subTotalAfterDiscount = 0;
                let discountAmount = 0;
                let taxTotal = 0;
                let grandTotal = 0;
                let appliedTaxes = [];
                d.items.forEach(row => {
                    subTotal += app.round(row.total, 'total');

                    if (_.isObject(row.taxDetail)) {
                        taxTotal += app.round(row.taxDetail.taxTotal, 'total');

                        row.taxDetail.applied.forEach(tax => {
                            const taxIndex = _.findIndex(appliedTaxes, t => t._id === tax._id);

                            if (taxIndex !== -1) {
                                appliedTaxes[taxIndex].unAppliedAmount += app.round(tax.unAppliedAmount || 0, 'total');
                                appliedTaxes[taxIndex].appliedAmount += app.round(tax.appliedAmount || 0, 'total');
                            } else {
                                tax.unAppliedAmount = app.round(tax.unAppliedAmount || 0, 'total');
                                tax.appliedAmount = app.round(tax.appliedAmount || 0, 'total');
                                appliedTaxes.push(_.cloneDeep(tax));
                            }
                        });
                    }
                });
                subTotalAfterDiscount = app.round(subTotal, 'total');
                grandTotal = app.round(subTotal + taxTotal, 'total');
                if (_.isNumber(discount) && subTotal > 0 && discount > 0) {
                    // Get discount amount.
                    discountAmount = app.round((subTotal * discount) / 100, 'total');

                    // Calculate new taxes and tax total.
                    const ratio = discountAmount / subTotal;
                    const payload = {items: []};
                    d.items.forEach(row => {
                        const rowDiscountAmount = app.round(ratio * row.total, 'total');
                        const newRowTotal = app.round(row.total - rowDiscountAmount, 'total');
                        const key = row.taxDetail.applied.map(t => t._id).join('');
                        const existingIndex = _.findIndex(payload.items, i => i.key === key);
                        if (existingIndex === -1) {
                            payload.items.push({
                                taxId: row.taxId,
                                quantity: row.quantity || 1,
                                taxPayload: row.taxPayload,
                                amount: newRowTotal,
                                key
                            });
                        } else {
                            payload.items[existingIndex].amount += newRowTotal;
                            payload.items[existingIndex].quantity += row.quantity || 1;
                        }
                    });
                    const result = await app.rpc(
                        'kernel.common.calculate-taxes',
                        {...payload, currencyRate: d.currencyRate || 1},
                        {
                            currencyRate: d.currencyRate || 1
                        }
                    );
                    taxTotal = 0;
                    for (const r of result) {
                        taxTotal += app.round(r.amount, 'total');
                    }
                    appliedTaxes = appliedTaxes.map(tax => {
                        const newTaxResult = result.find(r => r.taxId === tax._id);

                        tax.appliedAmount = app.round(newTaxResult.amount, 'total');

                        return tax;
                    });

                    // Calculate subtotal after discount.
                    subTotalAfterDiscount = app.round(subTotal - discountAmount, 'total');

                    // Calculate new grand total.
                    grandTotal = app.round(subTotal + taxTotal - discountAmount, 'total');
                }
                d.subTotal = app.round(subTotal, 'total');
                d.discount = discount;
                d.discountAmount = discountAmount;
                d.subTotalAfterDiscount = subTotalAfterDiscount;
                d.taxTotal = app.round(taxTotal, 'total');
                d.grandTotal = app.round(grandTotal, 'total');
                d.appliedTaxes = appliedTaxes;

                // Init contract params.
                const contractIds = _.uniq(d.items.map(item => item.contractId));
                if (contractIds.length > 0) {
                    const contracts = await app.collection('purchase.contracts').find({
                        _id: {$in: contractIds},
                        $select: ['paymentTermId', 'guaranteeIds']
                    });
                    const currentPaymentTermId = d.paymentTermId;
                    const params = {paymentTermIds: [], guaranteeIds: []};

                    for (const contract of contracts) {
                        if (!!contract.paymentTermId) {
                            params.paymentTermIds.push(contract.paymentTermId);
                        }

                        if ((contract.guaranteeIds || []).length > 0) {
                            params.guaranteeIds.push(...contract.guaranteeIds);
                        }
                    }

                    params.paymentTermIds = _.uniq(params.paymentTermIds);
                    params.guaranteeIds = _.uniq(params.guaranteeIds);

                    if (params.paymentTermIds.length > 0) {
                        d.paymentTermId = params.paymentTermIds[0];
                    }

                    if (params.guaranteeIds.length > 0) {
                        d.guaranteeId = params.guaranteeIds[0];
                    }

                    if (currentPaymentTermId !== d.paymentTermId) {
                        d.paymentTerm = await app.collection('finance.payment-terms').findOne({
                            _id: d.paymentTermId
                        });
                    }

                    d.contractParams = params;
                }

                // Init limit params.
                if (conversionType !== 'to-quotation') {
                    d.limitParams = await app.rpc('finance.check-partner-limit', {
                        partnerId: d.partnerId,
                        currencyId: d.currencyId,
                        guaranteeId: d.guaranteeId,
                        amount: d.grandTotal,
                        document: conversionType === 'to-order' ? 'order' : 'invoice'
                    });
                }

                // Fix related documents.
                const addedRequestsIds = [];
                d.relatedDocuments = d.relatedDocuments.filter(rd => {
                    if (addedRequestsIds.indexOf(rd.ids[0]) !== -1) {
                        return false;
                    }

                    addedRequestsIds.push(rd.ids[0]);

                    return true;
                });

                // Sync additional informations
                if (conversionType === 'to-quotation' || conversionType === 'to-order') {
                    const purchaseRequest = await app.collection('purchase.requests').findOne({
                        _id: documentItems[0].requestId,
                        $select: ['relatedDocuments'],
                        $disableActiveCheck: true,
                        $disableSoftDelete: true
                    });

                    if (
                        purchaseRequest &&
                        Array.isArray(purchaseRequest.relatedDocuments) &&
                        purchaseRequest.relatedDocuments.length > 0
                    ) {
                        const documentTypes = [
                            {
                                type: 'sale.orders',
                                collection: 'sale.orders'
                            },
                            {
                                type: 'sale.quotations',
                                collection: 'sale.quotations'
                            }
                        ];

                        for (const documentType of documentTypes) {
                            const relatedDocumentIds = purchaseRequest.relatedDocuments.find(
                                rd => rd.collection === documentType.type
                            )?.ids;

                            if (Array.isArray(relatedDocumentIds) && relatedDocumentIds.length > 0) {
                                const relatedDocument = await app.collection(documentType.collection).findOne({
                                    _id: relatedDocumentIds[0],
                                    $select: ['additionalInformation', 'additionalInformationId'],
                                    $disableActiveCheck: true,
                                    $disableSoftDelete: true
                                });

                                if (
                                    !relatedDocument ||
                                    !relatedDocument.additionalInformationId ||
                                    _.isEmpty(relatedDocument.additionalInformation)
                                ) {
                                    continue;
                                }

                                const sourceDocument = await app.collection('kernel.additional-information').findOne({
                                    _id: relatedDocument.additionalInformationId,
                                    $select: ['fields']
                                });

                                const targetType =
                                    conversionType === 'to-quotation' ? 'purchase-quotation' : 'purchase-order';

                                const targetDocument = await app
                                    .collection('kernel.additional-information')
                                    .findOne({type: targetType, $select: ['fields']});

                                d.additionalInformation = mergeAdditionalInformation({
                                    sourceData: relatedDocument.additionalInformation,
                                    sourceDocument,
                                    targetDocument
                                });
                            }
                        }
                    }
                }

                // Create document.
                let result = null;
                if (conversionType === 'to-quotation') {
                    d.expiryDate = app.datetime
                        .fromJSDate(d.recordDate)
                        .plus({
                            days: app.setting('purchase.defaultQuotationValidityDayCount')
                        })
                        .toJSDate();

                    result = await app.collection('purchase.quotations').create(d);

                    for (const addedRequestsId of addedRequestsIds) {
                        requestRelatedDocumentsToAdd.push({
                            requestId: addedRequestsId,
                            collection: 'purchase.quotations',
                            view: 'purchase.purchase.quotations',
                            title: 'Quotations',
                            id: result._id
                        });
                    }
                    // for (const addedRequestsId of addedRequestsIds) {
                    //     const rds =
                    //         (
                    //             await app.collection('purchase.requests').findOne({
                    //                 _id: addedRequestsId,
                    //                 $select: ['relatedDocuments']
                    //             })
                    //         ).relatedDocuments || [];
                    //
                    //     const existingIndex = rds.findIndex(rd => rd.collection === 'purchase.quotations');
                    //     if (existingIndex !== -1) {
                    //         rds[existingIndex].ids.push(result._id);
                    //     } else {
                    //         rds.push({
                    //             collection: 'purchase.quotations',
                    //             view: 'purchase.purchase.quotations',
                    //             title: 'Quotations',
                    //             ids: [result._id]
                    //         });
                    //     }
                    //
                    //     requestOperations.push({
                    //         updateOne: {
                    //             filter: {_id: addedRequestsId},
                    //             update: {$set: {relatedDocuments: rds}}
                    //         }
                    //     });
                    // }
                } else if (conversionType === 'to-order') {
                    result = await app.collection('purchase.orders').create(d);

                    for (const addedRequestsId of addedRequestsIds) {
                        requestRelatedDocumentsToAdd.push({
                            requestId: addedRequestsId,
                            collection: 'purchase.orders',
                            view: 'purchase.purchase.orders',
                            title: 'Orders',
                            id: result._id
                        });
                    }
                    // for (const addedRequestsId of addedRequestsIds) {
                    //     const rds =
                    //         (
                    //             await app.collection('purchase.requests').findOne({
                    //                 _id: addedRequestsId,
                    //                 $select: ['relatedDocuments']
                    //             })
                    //         ).relatedDocuments || [];
                    //
                    //     const existingIndex = rds.findIndex(rd => rd.collection === 'purchase.orders');
                    //     if (existingIndex !== -1) {
                    //         rds[existingIndex].ids.push(result._id);
                    //     } else {
                    //         rds.push({
                    //             collection: 'purchase.orders',
                    //             view: 'purchase.purchase.orders',
                    //             title: 'Orders',
                    //             ids: [result._id]
                    //         });
                    //     }
                    //
                    //     requestOperations.push({
                    //         updateOne: {
                    //             filter: {_id: addedRequestsId},
                    //             update: {$set: {relatedDocuments: rds}}
                    //         }
                    //     });
                    // }
                } else {
                    result = await app.collection('accounting.vendor-invoices').create(d);

                    for (const addedRequestsId of addedRequestsIds) {
                        requestRelatedDocumentsToAdd.push({
                            requestId: addedRequestsId,
                            collection: 'accounting.vendor-invoices',
                            view: 'accounting.purchase.vendor-invoices',
                            title: 'Invoices',
                            id: result._id
                        });
                    }
                    // for (const addedRequestsId of addedRequestsIds) {
                    //     const rds =
                    //         (
                    //             await app.collection('purchase.requests').findOne({
                    //                 _id: addedRequestsId,
                    //                 $select: ['relatedDocuments']
                    //             })
                    //         ).relatedDocuments || [];
                    //
                    //     const existingIndex = rds.findIndex(rd => rd.collection === 'accounting.vendor-invoices');
                    //     if (existingIndex !== -1) {
                    //         rds[existingIndex].ids.push(result._id);
                    //     } else {
                    //         rds.push({
                    //             collection: 'accounting.vendor-invoices',
                    //             view: 'accounting.purchase.vendor-invoices',
                    //             title: 'Invoices',
                    //             ids: [result._id]
                    //         });
                    //     }
                    //
                    //     requestOperations.push({
                    //         updateOne: {
                    //             filter: {_id: addedRequestsId},
                    //             update: {$set: {relatedDocuments: rds}}
                    //         }
                    //     });
                    // }
                }

                for (const documentItem of documentItems) {
                    requestProcurementsOperations.push({
                        updateOne: {
                            filter: {_id: documentItem._id},
                            update: {
                                $set: {
                                    destinationType:
                                        conversionType === 'to-quotation'
                                            ? 'quotation'
                                            : conversionType === 'to-order'
                                            ? 'order'
                                            : 'invoice',
                                    destinationCode: result.code,
                                    destinationId: result._id,
                                    status: 'processing'
                                }
                            }
                        }
                    });
                }
            }

            const requests = await app.collection('purchase.requests').find({
                _id: {$in: _.uniq(requestRelatedDocumentsToAdd.map(rd => rd.requestId))},
                $select: ['_id', 'relatedDocuments']
            });
            for (const request of requests) {
                const rds = request.relatedDocuments ?? [];
                const rdsToAdd = requestRelatedDocumentsToAdd.filter(rd => rd.requestId === request._id);
                if (rdsToAdd.length < 1) {
                    continue;
                }

                for (const rdToAdd of rdsToAdd) {
                    const existingIndex = rds.findIndex(rd => rd.collection === rdToAdd.collection);

                    if (existingIndex !== -1) {
                        rds[existingIndex].ids.push(rdToAdd.id);
                    } else {
                        rds.push({
                            collection: rdToAdd.collection,
                            view: rdToAdd.view,
                            title: rdToAdd.title,
                            ids: [rdToAdd.id]
                        });
                    }
                }

                requestOperations.push({
                    updateOne: {
                        filter: {_id: request._id},
                        update: {$set: {relatedDocuments: rds}}
                    }
                });
            }

            if (requestOperations.length > 0) {
                await app.collection('purchase.requests').bulkWrite(requestOperations);
            }

            if (requestProcurementsOperations.length > 0) {
                await app.collection('purchase.request-procurements').bulkWrite(requestProcurementsOperations);
            }
        }
    },
    {
        name: 'request-procurements-transport',
        async action(payload, params) {
            const app = this.app;
            const user = params.user;
            const numbering = await app.collection('kernel.numbering').findOne(
                {
                    code: 'stockTransportNumbering',
                    $select: ['_id']
                },
                {
                    disableInUseCheck: true,
                    disableActiveCheck: true,
                    disableSoftDelete: true
                }
            );
            const {items} = payload;
            const requestProcurementsOperations = [];
            const requestOperations = [];

            const warehouses = await app.collection('inventory.warehouses').find({
                _id: {
                    $in: _.uniq(items.map(i => i.sourceWarehouseId).concat(items.map(i => i.destinationWarehouseId)))
                },
                $select: ['_id', 'branchId']
            });
            const warehousesMap = {};
            for (const warehouse of warehouses) {
                warehousesMap[warehouse._id] = warehouse;
            }

            const groupedDocuments = Object.values(
                _.groupBy(items, i => {
                    return i.sourceWarehouseId + '|' + i.destinationWarehouseId;
                })
            );

            for (const documentItems of groupedDocuments) {
                const sourceWarehouse = warehousesMap[documentItems[0].sourceWarehouseId];
                const destinationWarehouse = warehousesMap[documentItems[0].destinationWarehouseId];
                const d = {};

                d.status = 'requested';
                d.code = await app.rpc('kernel.common.request-number', {
                    numberingId: numbering._id,
                    save: true
                });
                d.sourceWarehouseId = sourceWarehouse._id;
                d.destinationWarehouseId = destinationWarehouse._id;
                d.sourceBranchId = sourceWarehouse.branchId;
                d.destinationBranchId = destinationWarehouse.branchId;
                d.date = app.datetime.local().toJSDate();
                d.createdBy = user._id;
                d.requestedBy = user._id;
                d.items = [];
                d.relatedDocuments = [];

                for (const documentItem of documentItems) {
                    const item = {};

                    item.id = Random.id(16);
                    item.productId = documentItem.productId;
                    item.unitId = documentItem.unitId;
                    item.financialProjectId = documentItem.financialProjectId;
                    item.sourceStockQuantity = documentItem.sourceStockQuantity;
                    item.destinationStockQuantity = documentItem.destinationStockQuantity;
                    item.qty = documentItem.quantity;
                    item.baseQty = documentItem.baseQuantity;

                    d.relatedDocuments.push({
                        collection: 'purchase.requests',
                        view: 'purchase.purchase.requests',
                        title: 'Requests',
                        ids: [documentItem.requestId]
                    });

                    requestProcurementsOperations.push({
                        updateOne: {
                            filter: {_id: documentItem._id},
                            update: {
                                $set: {
                                    destinationItemId: item.id
                                }
                            }
                        }
                    });

                    d.items.push(item);
                }

                // Fix related documents.
                const addedRequestsIds = [];
                d.relatedDocuments = d.relatedDocuments.filter(rd => {
                    if (addedRequestsIds.indexOf(rd.ids[0]) !== -1) {
                        return false;
                    }

                    addedRequestsIds.push(rd.ids[0]);

                    return true;
                });

                const result = await app.collection('inventory.transports').create(d);

                for (const addedRequestsId of addedRequestsIds) {
                    const rds =
                        (
                            await app.collection('purchase.requests').findOne({
                                _id: addedRequestsId,
                                $select: ['relatedDocuments']
                            })
                        ).relatedDocuments || [];

                    const existingIndex = rds.findIndex(rd => rd.collection === 'inventory.transports');
                    if (existingIndex !== -1) {
                        rds[existingIndex].ids.push(result._id);
                    } else {
                        rds.push({
                            collection: 'inventory.transports',
                            view: 'inventory.operations.transports',
                            title: 'Stock Transports',
                            ids: [result._id]
                        });
                    }

                    requestOperations.push({
                        updateOne: {
                            filter: {_id: addedRequestsId},
                            update: {$set: {relatedDocuments: rds}}
                        }
                    });
                }

                for (const documentItem of documentItems) {
                    requestProcurementsOperations.push({
                        updateOne: {
                            filter: {_id: documentItem._id},
                            update: {
                                $set: {
                                    destinationType: 'transport',
                                    destinationCode: result.code,
                                    destinationId: result._id,
                                    status: 'processing'
                                }
                            }
                        }
                    });
                }
            }

            if (requestOperations.length > 0) {
                await app.collection('purchase.requests').bulkWrite(requestOperations);
            }

            if (requestProcurementsOperations.length > 0) {
                await app.collection('purchase.request-procurements').bulkWrite(requestProcurementsOperations);
            }
        }
    },
    {
        name: 'request-procurements-cancel',
        async action({ids}, params) {
            const app = this.app;
            const user = params.user;
            const items = await app.collection('purchase.request-procurements').find({
                _id: {$in: ids},
                $select: ['_id', 'requestId', 'requestItemId', 'openQuantity', 'cancelingReason', 'canceledBy']
            });
            const requests = await app.collection('purchase.requests').find({
                _id: {$in: _.uniq(items.map(item => item.requestId))},
                $select: ['_id', 'items']
            });
            const requestProcurementsOperations = [];
            const requestOperations = [];

            for (const item of items) {
                requestProcurementsOperations.push({
                    updateOne: {
                        filter: {_id: item._id},
                        update: {
                            $set: {
                                canceledQuantity: item.openQuantity,
                                status: 'canceled'
                            }
                        }
                    }
                });

                const request = requests.find(request => request._id === item.requestId);
                const requestItems = request.items.map(ri => {
                    if (ri.id === item.requestItemId) {
                        ri.status = 'canceled';
                        ri.cancelingReason = item.cancelingReason;
                        ri.canceledBy = item.canceledBy;
                    }

                    return ri;
                });

                requestOperations.push({
                    updateOne: {
                        filter: {_id: item.requestId},
                        update: {$set: {items: requestItems}}
                    }
                });
            }

            if (requestOperations.length > 0) {
                await app.collection('purchase.requests').bulkWrite(requestOperations);
            }

            const updatedRequest = await app.collection('purchase.requests').find({
                _id: {$in: requests.map(request => request._id)},
                $select: ['_id', 'items']
            });
            const updatedRequestOperations = [];
            const requestIdsToUpdate = [];
            for (const request of updatedRequest) {
                if (request.items.every(request => request.status === 'canceled')) {
                    updatedRequestOperations.push({
                        updateOne: {
                            filter: {_id: request._id},
                            update: {$set: {status: 'canceled'}}
                        }
                    });
                    requestIdsToUpdate.push(request._id);
                }
            }
            if (updatedRequestOperations.length > 0) {
                await app.collection('purchase.requests').bulkWrite(updatedRequestOperations);
            }
            // For MRP.
            if (requestIdsToUpdate.length > 0) {
                await app.collection('purchase.requests').patch(
                    {_id: {$in: requestIdsToUpdate}},
                    {
                        updatedAt: app.datetime.local().toJSDate()
                    },
                    {skipEvents: true}
                );
            }

            if (requestProcurementsOperations.length > 0) {
                await app.collection('purchase.request-procurements').bulkWrite(requestProcurementsOperations);
            }
        }
    }
];
