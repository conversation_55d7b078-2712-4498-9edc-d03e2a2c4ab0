export default {
    name: 'request-procurements',
    title: 'Request Procurements',
    uid: false,
    timestamps: false,
    softDelete: false,
    branch: true,
    schema: {
        status: {
            type: 'string',
            label: 'Status',
            allowed: ['open', 'processing', 'closed', 'canceled'],
            default: 'draft',
            index: true
        },
        requestId: {
            type: 'string',
            label: 'Request',
            index: true
        },
        requestItemId: {
            type: 'string',
            label: 'Request item',
            index: true
        },
        requestedByType: {
            type: 'string',
            label: 'Requested by type',
            default: 'system',
            index: true
        },
        requestedByEmployeeId: {
            type: 'string',
            label: 'Requested by',
            required: false,
            index: true
        },
        relatedPartnerId: {
            type: 'string',
            label: 'Related partner',
            index: true,
            required: false
        },
        requestedByUserId: {
            type: 'string',
            label: 'Requested by',
            required: false,
            index: true
        },
        priority: {
            type: 'string',
            label: 'Priority',
            allowed: ['not-urgent', 'normal', 'urgent', 'very-urgent'],
            default: 'normal',
            index: true
        },
        priorityValue: {
            type: 'integer',
            label: 'Priority value',
            default: 1,
            index: true
        },
        productId: {
            type: 'string',
            label: 'Product',
            index: true
        },
        productGroupIds: {
            type: ['string'],
            label: 'Product groups',
            index: true
        },
        productType: {
            type: 'string',
            label: 'Product type',
            required: false,
            index: true
        },
        barcode: {
            type: 'string',
            label: 'Barcode',
            required: false,
            index: true
        },
        supplierCatalogNo: {
            type: 'string',
            label: 'Supplier catalog no',
            required: false
        },
        warehouseId: {
            type: 'string',
            label: 'Warehouse',
            required: false,
            index: true
        },
        vendorId: {
            type: 'string',
            label: 'Vendor',
            index: true,
            required: false
        },
        unitId: {
            type: 'string',
            label: 'Unit',
            required: false,
            index: true
        },
        quantity: {
            type: 'decimal',
            label: 'Quantity',
            min: 0,
            index: true
        },
        approvedQuantity: {
            type: 'decimal',
            label: 'Approved quantity',
            min: 0,
            index: true
        },
        openQuantity: {
            type: 'decimal',
            label: 'Open quantity',
            min: 0,
            default: 0,
            index: true
        },
        completedQuantity: {
            type: 'decimal',
            label: 'Completed quantity',
            min: 0,
            default: 0,
            index: true
        },
        canceledQuantity: {
            type: 'decimal',
            label: 'Canceled quantity',
            min: 0,
            default: 0,
            index: true
        },
        baseUnitId: {
            type: 'string',
            label: 'Base unit',
            required: false,
            index: true
        },
        baseQuantity: {
            type: 'decimal',
            label: 'Base Quantity',
            default: 1,
            index: true
        },
        unitPrice: {
            type: 'decimal',
            label: 'Unit Price',
            default: 0,
            index: true
        },
        discount: {
            type: 'decimal',
            label: 'Discount %',
            default: 0,
            index: true
        },
        unitPriceAfterDiscount: {
            type: 'decimal',
            label: 'Unit Price After Discount',
            default: 0,
            index: true
        },
        grossUnitPriceAfterDiscount: {
            type: 'decimal',
            label: 'Gross Unit Price After Discount',
            default: 0,
            index: true
        },
        taxId: {
            type: 'string',
            label: 'Tax',
            required: false,
            index: true
        },
        taxTotal: {
            type: 'decimal',
            label: 'Tax Total',
            default: 0,
            index: true
        },
        grossTotal: {
            type: 'decimal',
            label: 'Gross Total',
            default: 0,
            index: true
        },
        realTotal: {
            type: 'decimal',
            label: 'Real Total',
            default: 0,
            index: true
        },
        total: {
            type: 'decimal',
            label: 'Total',
            default: 0,
            index: true
        },
        currencyId: {
            type: 'string',
            label: 'Currency',
            required: false,
            index: true
        },
        currencyRate: {
            type: 'decimal',
            label: 'Currency rate',
            default: 1
        },
        requiredDate: {
            type: 'date',
            label: 'Required date',
            default: 'date:now',
            index: true
        },
        recordDate: {
            type: 'date',
            label: 'Record date',
            default: 'date:now',
            index: true
        },
        issueDate: {
            type: 'date',
            label: 'Issue date',
            default: 'date:now',
            index: true
        },
        expiryDate: {
            type: 'date',
            label: 'Expiry date',
            default: 'date:now',
            index: true
        },
        destinationType: {
            type: 'string',
            label: 'Destination type',
            required: false
        },
        destinationCode: {
            type: 'string',
            label: 'Destination ode',
            required: false
        },
        destinationId: {
            type: 'string',
            label: 'Destination',
            required: false,
            index: true
        },
        destinationItemId: {
            type: 'string',
            label: 'Destination item',
            required: false,
            index: true
        },
        financialProjectId: {
            type: 'string',
            label: 'Project',
            required: false,
            index: true
        },
        pcmModelId: {
            type: 'string',
            required: false
        },
        pcmConfigurationId: {
            type: 'string',
            required: false
        },
        cancelingReason: {
            type: 'string',
            label: 'Canceling reason',
            required: false
        },
        canceledBy: {
            type: 'string',
            label: 'Canceled by',
            required: false
        },
        request: {
            type: 'object',
            blackbox: true,
            required: false
        },
        requestedByEmployee: {
            type: 'object',
            blackbox: true,
            required: false
        },
        requestedByUser: {
            type: 'object',
            blackbox: true,
            required: false
        },
        branch: {
            type: 'object',
            blackbox: true,
            required: false
        },
        product: {
            type: 'object',
            blackbox: true,
            required: false
        },
        vendor: {
            type: 'object',
            blackbox: true,
            required: false
        },
        warehouse: {
            type: 'object',
            blackbox: true,
            required: false
        },
        unit: {
            type: 'object',
            blackbox: true,
            required: false
        },
        baseUnit: {
            type: 'object',
            blackbox: true,
            required: false
        },
        tax: {
            type: 'object',
            blackbox: true,
            required: false
        },
        organizationId: {
            type: 'string',
            label: 'Organization',
            required: false,
            index: true
        },
        purchaseManagerId: {
            type: 'string',
            label: 'Purchase manager',
            required: false,
            index: true
        },
        purchaseRepresentativeId: {
            type: 'string',
            label: 'Purchase representative',
            required: false,
            index: true
        },
        allowedVendorIds: {
            type: ['string'],
            default: []
        },
        deliveryNote: {
            type: 'string',
            label: 'Delivery note',
            required: false
        }
    },
    attributes: {
        currency: {
            collection: 'kernel.currencies',
            parentField: 'currencyId',
            childField: '_id'
        },
        organization: {
            collection: 'kernel.organizations',
            parentField: 'organizationId',
            childField: '_id'
        },
        purchaseManager: {
            collection: 'kernel.partners',
            parentField: 'purchaseManagerId',
            childField: '_id'
        },
        purchaseRepresentative: {
            collection: 'kernel.partners',
            parentField: 'purchaseRepresentativeId',
            childField: '_id'
        },
        relatedPartner: {
            collection: 'kernel.partners',
            parentField: 'relatedPartnerId',
            childField: '_id'
        }
    }
};
